const Redis = require('ioredis');
const { RateLimiterRedis } = require('rate-limiter-flexible');

const KEY_PREFIX = 'sw:rate-limit:';
const TIMEOUT_FLAG = '__RL_TIMEOUT__';
const DEFAULT_SOFT_TIMEOUT_MS = 5000;
const SHUTDOWN_TIMEOUT_MS = 2000;

class RateLimiterService {
    constructor() {
        this._client = null;
        this._initPromise = null;
    }

    _initClient() {
        if (this._initPromise) return this._initPromise;

        this._initPromise = (async () => {
            try {
                const redisUrl = sails.config.connections.redis;
                const clientOptions = {
                    maxRetriesPerRequest: 1,
                    enableOfflineQueue: false,
                    connectTimeout: 800,
                    lazyConnect: true,
                    keyPrefix: KEY_PREFIX,
                };

                const client = new Redis(redisUrl, clientOptions);

                client.on('ready', () => {
                    sails.log.verbose('RateLimiterService Redis ready');
                });

                client.on('error', (err) => {
                    sails.log.warn('RateLimiterService Redis error:', err && (err.code || err.message));
                });

                await client.connect();
                sails.once('lowering', () => this._shutdown());

                this._client = client;
                return true;
            } catch (err) {
                sails.log.error('RateLimiterService init failed:', err);
                this._client = null;
                this._initPromise = null;
                return false;
            }
        })();

        return this._initPromise;
    }

    async _shutdown() {
        if (!this._client) return;
        try {
            sails.log.verbose('RateLimiterService shutdown');
            const timeout = new Promise((res) => setTimeout(res, SHUTDOWN_TIMEOUT_MS, TIMEOUT_FLAG));
            const quit = this._client.quit();
            const result = await Promise.race([quit, timeout]);
            if (result === TIMEOUT_FLAG) {
                sails.log.warn('RateLimiterService quit timeout, disconnecting');
                this._client.disconnect();
            }
        } catch (err) {
            sails.log.warn('RateLimiterService shutdown error:', err && err.message);
            this._client.disconnect();
        }
    }

    _softTimeout(promise, ms) {
        let timeout;
        return Promise.race([
            promise,
            new Promise((res) => {
                timeout = setTimeout(() => res(TIMEOUT_FLAG), ms)
            }),
        ]).finally(() => {
            // Clearing timeout to prevent extra invocation of the timeout function, despite the race condition
            if (timeout) clearTimeout(timeout);
        });
    }

    _createRateLimiter(rateLimiterName, config) {
        return new RateLimiterRedis({
            storeClient: this._client,
            keyPrefix: rateLimiterName,
            points: config.points,
            duration: config.duration,
            blockDuration: config.blockDuration || 0,
            execEvenly: config.execEvenly === undefined ? true : config.execEvenly,
        });
    }

    async _getKeySelectorSafe(getKeySelector, req) {
        try {
            return await getKeySelector(req);
        } catch (err) {
            return null;
        }
    }

    createRateLimiter(rateLimiterName, config, getKeySelector) {
        const hasSoftTimeoutConfig = config.softTimeoutMs !== undefined && config.softTimeoutMs !== null;
        const softTimeoutMs = hasSoftTimeoutConfig ? config.softTimeoutMs : DEFAULT_SOFT_TIMEOUT_MS;
        let limiter = null;

        return async (req, res, next) => {
            if (req.method === 'OPTIONS') return next();

            const keySelector = await this._getKeySelectorSafe(getKeySelector, req);
            if (!keySelector) return next();

            try {
                await this._initClient();
                if (!this._client || this._client.status !== 'ready') return next();

                if (!limiter) {
                    limiter = this._createRateLimiter(rateLimiterName, config);
                }

                const result = await this._softTimeout(limiter.consume(keySelector), softTimeoutMs);
                if (result === TIMEOUT_FLAG) {
                    sails.log.warn('RateLimiterService soft timeout', {
                        rateLimiter: rateLimiterName,
                        keySelector: keySelector,
                        path: req.path,
                    });
                    return next();
                }

                const remaining = (result && result.remainingPoints) || 0;
                const msBeforeNext = (result && result.msBeforeNext) || 0;

                res.set({
                    'RateLimit-Limit': String(config.points),
                    'RateLimit-Remaining': String(remaining),
                    'RateLimit-Reset': String(Math.ceil(msBeforeNext / 1000)),
                });

                return next();
            } catch (err) {
                if (err && err.remainingPoints !== undefined) {
                    const msBeforeNext = err.msBeforeNext || 0;
                    const retryAfter = Math.ceil(msBeforeNext / 1000);

                    res.set({
                        'RateLimit-Limit': String(config.points),
                        'RateLimit-Remaining': '0',
                        'RateLimit-Reset': String(retryAfter),
                        'Retry-After': String(retryAfter),
                    });

                    sails.log.info('Rate limit exceeded', {
                        rateLimiter: rateLimiterName,
                        keySelector: keySelector,
                        method: req.method,
                        path: req.path,
                        retryAfterMs: msBeforeNext,
                    });

                    if (config.blockDuration > 0) {
                        sails.log.warn('Rate limit block applied', {
                            rateLimiter: rateLimiterName,
                            keySelector: keySelector,
                            path: req.path,
                            blockDuration: config.blockDuration,
                        });
                    }

                    return res.status(429).json({
                        error: 'rate_limited',
                        rateLimiter: rateLimiterName,
                        retryAfterMs: msBeforeNext,
                    });
                }

                sails.log.warn('RateLimiterService error (failing open):', {
                    rateLimiter: rateLimiterName,
                    keySelector: keySelector,
                    path: req.path,
                    error: (err && (err.code || err.message)) || String(err),
                });

                return next();
            }
        };
    }
}

module.exports = new RateLimiterService();
